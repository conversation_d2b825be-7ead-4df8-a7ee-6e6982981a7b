class Player extends Phaser.Physics.Arcade.Sprite {
    constructor(scene, x, y, team, position, number) {
        // Determine sprite key based on position
        let spriteKey = position === PlayerPositions.GOALKEEPER ? 'player-goalkeeper' : 
                       team === Teams.TEAM_1 ? 'player-team1' : 'player-team2';
        
        super(scene, x, y, spriteKey);
        
        // Add to scene and physics world
        scene.add.existing(this);
        scene.physics.add.existing(this);

        // Properties
        this.team = team;
        this.position = position;
        this.number = number;
        this.isSelected = false;
        this.isExcluded = false;
        this.exclusionTime = 0;
        
        // Movement properties
        this.moveSpeed = GameConfig.PLAYER.MAX_SPEED;
        this.acceleration = GameConfig.PLAYER.ACCELERATION;
        this.isMoving = false;
        this.targetX = x;
        this.targetY = y;
        
        // Ball interaction
        this.hasBall = false;
        this.canPass = true;
        this.canShoot = true;
        this.lastActionTime = 0;
        
        // AI properties
        this.aiTarget = null;
        this.aiState = 'idle';
        this.reactionTime = GameConfig.AI.REACTION_TIME;
        
        // Power-ups (for arcade mode)
        this.powerUps = {
            turbo: { active: false, timeLeft: 0 },
            fastShot: { active: false, timeLeft: 0 }
        };
        
        // Visual elements
        this.selectionIndicator = null;
        this.numberText = null;
        
        this.createVisualElements();
        this.setupPhysics();
    }
    
    createVisualElements() {
        // Selection indicator
        this.selectionIndicator = this.scene.add.image(this.x, this.y, 'player-selected');
        this.selectionIndicator.setVisible(false);
        
        // Player number
        if (GameConfig.PLAYER.SHOW_NUMBERS) {
            this.numberText = this.scene.add.text(this.x, this.y, this.number.toString(), {
                fontSize: `${GameConfig.PLAYER.NUMBER_SIZE}px`,
                fontFamily: 'Arial',
                color: '#ffffff',
                fontStyle: 'bold',
                stroke: '#000000',
                strokeThickness: 2
            }).setOrigin(0.5);
        }
    }
    
    setupPhysics() {
        // Set physics properties
        this.setCollideWorldBounds(true);
        this.setBounce(0.3);
        this.setDrag(GameConfig.PLAYER.DRAG * 100);
        this.setMaxVelocity(this.moveSpeed);
        
        // Set circular collision body
        this.body.setCircle(GameConfig.PLAYER.RADIUS);
    }
    
    update(time, delta) {
        // Update exclusion time
        if (this.isExcluded) {
            this.exclusionTime -= delta / 1000;
            if (this.exclusionTime <= 0) {
                this.returnFromExclusion();
            }
        }
        
        // Update power-ups
        this.updatePowerUps(delta);
        
        // Update visual elements
        this.updateVisualElements();
        
        // Apply water resistance
        this.applyWaterResistance();
    }
    
    updatePowerUps(delta) {
        Object.keys(this.powerUps).forEach(powerUpType => {
            const powerUp = this.powerUps[powerUpType];
            if (powerUp.active) {
                powerUp.timeLeft -= delta / 1000;
                if (powerUp.timeLeft <= 0) {
                    this.deactivatePowerUp(powerUpType);
                }
            }
        });
    }
    
    updateVisualElements() {
        // Update selection indicator position
        if (this.selectionIndicator) {
            this.selectionIndicator.setPosition(this.x, this.y);
        }
        
        // Update number text position
        if (this.numberText) {
            this.numberText.setPosition(this.x, this.y);
        }
        
        // Update alpha based on exclusion
        this.setAlpha(this.isExcluded ? 0.5 : 1.0);
    }
    
    applyWaterResistance() {
        // Simulate water resistance
        if (this.body.velocity.x !== 0 || this.body.velocity.y !== 0) {
            this.body.velocity.x *= GameConfig.PLAYER.DRAG;
            this.body.velocity.y *= GameConfig.PLAYER.DRAG;
        }
    }
    
    // Movement methods
    moveTowards(x, y) {
        if (this.isExcluded) return;
        
        this.targetX = x;
        this.targetY = y;
        this.isMoving = true;
        
        // Calculate direction
        const angle = Phaser.Math.Angle.Between(this.x, this.y, x, y);
        
        // Apply movement with current speed
        const currentSpeed = this.getCurrentSpeed();
        this.scene.physics.velocityFromAngle(
            Phaser.Math.RadToDeg(angle),
            currentSpeed,
            this.body.velocity
        );
    }
    
    moveInDirection(directionX, directionY) {
        if (this.isExcluded) return;
        
        // Normalize direction
        const length = Math.sqrt(directionX * directionX + directionY * directionY);
        if (length === 0) {
            this.stopMovement();
            return;
        }
        
        const normalizedX = directionX / length;
        const normalizedY = directionY / length;
        
        // Apply movement
        const currentSpeed = this.getCurrentSpeed();
        this.body.setVelocity(
            normalizedX * currentSpeed,
            normalizedY * currentSpeed
        );
        
        this.isMoving = true;
    }
    
    stopMovement() {
        this.body.setVelocity(0, 0);
        this.isMoving = false;
    }
    
    getCurrentSpeed() {
        let speed = this.moveSpeed;
        
        // Apply power-up modifiers
        if (this.powerUps.turbo.active) {
            speed *= GameConfig.ARCADE.TURBO_SPEED_MULTIPLIER;
        }
        
        return speed;
    }
    
    // Ball interaction methods
    passBall(targetX, targetY) {
        if (!this.hasBall || !this.canPass) return false;
        
        const ball = this.scene.ball;
        if (!ball || ball.holder !== this) return false;
        
        // Calculate pass force
        const distance = GameUtils.getDistance(this.x, this.y, targetX, targetY);
        const force = Math.min(GameConfig.BALL.PASS_FORCE, distance * 2);
        
        // Release ball and apply force
        ball.release();
        const angle = GameUtils.getAngle(this.x, this.y, targetX, targetY);
        
        ball.body.setVelocity(
            Math.cos(angle) * force,
            Math.sin(angle) * force
        );
        
        this.hasBall = false;
        this.lastActionTime = this.scene.time.now;
        
        // Reset shot clock
        this.scene.gameRules.resetShotClock();
        
        return true;
    }
    
    shootBall(targetX, targetY) {
        if (!this.hasBall || !this.canShoot) return false;
        
        const ball = this.scene.ball;
        if (!ball || ball.holder !== this) return false;
        
        // Calculate shoot force
        let force = GameConfig.BALL.SHOOT_FORCE;
        
        // Apply power-up modifiers
        if (this.powerUps.fastShot.active) {
            force *= GameConfig.ARCADE.FAST_SHOT_SPEED_MULTIPLIER;
        }
        
        // Release ball and apply force
        ball.release();
        const angle = GameUtils.getAngle(this.x, this.y, targetX, targetY);
        
        ball.body.setVelocity(
            Math.cos(angle) * force,
            Math.sin(angle) * force
        );
        
        this.hasBall = false;
        this.lastActionTime = this.scene.time.now;
        
        return true;
    }
    
    canPickupBall() {
        return !this.isExcluded && !this.hasBall;
    }
    
    pickupBall(ball) {
        if (!this.canPickupBall()) return false;
        
        this.hasBall = true;
        ball.setHolder(this);
        
        // Start shot clock if not already active
        this.scene.gameRules.startShotClock();
        
        return true;
    }
    
    // Selection methods
    setSelected(selected) {
        this.isSelected = selected;
        if (this.selectionIndicator) {
            this.selectionIndicator.setVisible(selected);
        }
    }
    
    // Exclusion methods
    exclude(duration = GameConfig.RULES.EXCLUSION_TIME) {
        this.isExcluded = true;
        this.exclusionTime = duration;
        
        // Drop ball if holding it
        if (this.hasBall) {
            const ball = this.scene.ball;
            if (ball) {
                ball.release();
            }
            this.hasBall = false;
        }
        
        // Move to exclusion area
        this.moveToExclusionArea();
        
        console.log(`Player ${this.number} excluded for ${duration} seconds`);
    }
    
    returnFromExclusion() {
        this.isExcluded = false;
        this.exclusionTime = 0;
        
        // Return to play area
        this.returnToPlayArea();
        
        console.log(`Player ${this.number} returned from exclusion`);
    }
    
    moveToExclusionArea() {
        // Move to side of pool
        const bounds = this.scene.poolBounds;
        const side = this.team === Teams.TEAM_1 ? 1 : -1;
        
        this.setPosition(
            bounds.x + bounds.width / 2 + side * (bounds.width / 2 + 50),
            bounds.y + bounds.height / 2
        );
    }
    
    returnToPlayArea() {
        // Return to appropriate position based on role
        const bounds = this.scene.poolBounds;
        const side = this.team === Teams.TEAM_1 ? 1 : -1;
        
        let x, y;
        
        switch (this.position) {
            case PlayerPositions.GOALKEEPER:
                x = bounds.x + bounds.width / 2 + side * (bounds.width / 2 - 30);
                y = bounds.y + bounds.height / 2;
                break;
            default:
                x = bounds.x + bounds.width / 2 + side * (bounds.width / 4);
                y = bounds.y + bounds.height / 2;
                break;
        }
        
        this.setPosition(x, y);
    }
    
    // Power-up methods
    activatePowerUp(type, duration) {
        if (!this.scene.arcadeMode) return;
        
        this.powerUps[type].active = true;
        this.powerUps[type].timeLeft = duration;
        
        // Apply visual effects
        this.applyPowerUpEffect(type);
        
        console.log(`Player ${this.number} activated ${type} power-up`);
    }
    
    deactivatePowerUp(type) {
        this.powerUps[type].active = false;
        this.powerUps[type].timeLeft = 0;
        
        // Remove visual effects
        this.removePowerUpEffect(type);
    }
    
    applyPowerUpEffect(type) {
        switch (type) {
            case 'turbo':
                this.setTint(0x00FF00);
                break;
            case 'fastShot':
                this.setTint(0xFF0000);
                break;
        }
    }
    
    removePowerUpEffect(type) {
        this.clearTint();
    }
    
    // AI methods
    setAITarget(target) {
        this.aiTarget = target;
    }
    
    setAIState(state) {
        this.aiState = state;
    }
    
    // Utility methods
    getDistanceTo(x, y) {
        return GameUtils.getDistance(this.x, this.y, x, y);
    }
    
    getAngleTo(x, y) {
        return GameUtils.getAngle(this.x, this.y, x, y);
    }
    
    isInGoalArea() {
        const bounds = this.scene.poolBounds;
        const sixMeterLine = GameConfig.POOL.SIX_METER_LINE;
        
        if (this.team === Teams.TEAM_1) {
            return this.x > bounds.x + bounds.width - sixMeterLine;
        } else {
            return this.x < bounds.x + sixMeterLine;
        }
    }
    
    isGoalkeeper() {
        return this.position === PlayerPositions.GOALKEEPER;
    }
    
    destroy() {
        // Clean up visual elements
        if (this.selectionIndicator) {
            this.selectionIndicator.destroy();
        }
        if (this.numberText) {
            this.numberText.destroy();
        }
        
        super.destroy();
    }
}
