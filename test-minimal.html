<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Polo Clash - Minimal Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1e3c72;
            color: white;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        #status {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            width: 80%;
            max-width: 800px;
        }
        .error { color: #ff6b6b; }
        .success { color: #51cf66; }
        .info { color: #74c0fc; }
        #game-container {
            border: 3px solid #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            background: #000;
        }
    </style>
</head>
<body>
    <h1>🎮 Water Polo Clash - Minimal Test</h1>
    
    <div id="status">
        <div class="info">Loading minimal game test...</div>
    </div>
    
    <div id="game-container">
        <canvas id="game-canvas"></canvas>
    </div>

    <!-- Load Phaser -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <!-- Load only essential game files -->
    <script src="js/config.js"></script>
    
    <script>
        const status = document.getElementById('status');
        
        function log(message, type = 'info') {
            console.log(message);
            const div = document.createElement('div');
            div.className = type;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            status.appendChild(div);
        }
        
        // Test minimal game with just basic scenes
        class MinimalPreloadScene extends Phaser.Scene {
            constructor() {
                super({ key: 'MinimalPreloadScene' });
            }
            
            preload() {
                log('MinimalPreloadScene: Starting preload...', 'info');
                
                // Create simple assets
                const graphics = this.add.graphics();
                
                // Player sprite
                graphics.fillStyle(0x3498DB);
                graphics.fillCircle(15, 15, 15);
                graphics.generateTexture('player', 30, 30);
                
                // Ball sprite
                graphics.clear();
                graphics.fillStyle(0xFFD700);
                graphics.fillCircle(10, 10, 10);
                graphics.generateTexture('ball', 20, 20);
                
                graphics.destroy();
                
                log('MinimalPreloadScene: Assets created', 'success');
                
                // Go to menu after short delay
                this.time.delayedCall(500, () => {
                    log('MinimalPreloadScene: Starting menu...', 'info');
                    this.scene.start('MinimalMenuScene');
                });
            }
        }
        
        class MinimalMenuScene extends Phaser.Scene {
            constructor() {
                super({ key: 'MinimalMenuScene' });
            }
            
            create() {
                log('MinimalMenuScene: Creating menu...', 'info');
                
                const width = this.cameras.main.width;
                const height = this.cameras.main.height;
                
                // Background
                this.add.rectangle(width / 2, height / 2, width, height, 0x1e3c72);
                
                // Title
                this.add.text(width / 2, height / 2 - 100, 'WATER POLO CLASH', {
                    fontSize: '48px',
                    fontFamily: 'Arial',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                // Subtitle
                this.add.text(width / 2, height / 2 - 50, 'Minimal Test Version', {
                    fontSize: '20px',
                    fontFamily: 'Arial',
                    color: '#3498DB'
                }).setOrigin(0.5);
                
                // Start button
                const startButton = this.add.text(width / 2, height / 2 + 50, 'START GAME', {
                    fontSize: '24px',
                    fontFamily: 'Arial',
                    color: '#51cf66',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                startButton.setInteractive();
                startButton.on('pointerover', () => startButton.setScale(1.1));
                startButton.on('pointerout', () => startButton.setScale(1));
                startButton.on('pointerdown', () => {
                    log('MinimalMenuScene: Starting game...', 'info');
                    this.scene.start('MinimalGameScene');
                });
                
                log('MinimalMenuScene: Menu created successfully', 'success');
            }
        }
        
        class MinimalGameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'MinimalGameScene' });
            }
            
            create() {
                log('MinimalGameScene: Creating game...', 'info');
                
                const width = this.cameras.main.width;
                const height = this.cameras.main.height;
                
                // Pool background
                this.add.rectangle(width / 2, height / 2, 800, 500, 0x4A90E2);
                this.add.rectangle(width / 2, height / 2, 800, 500).setStrokeStyle(4, 0xFFFFFF);
                
                // Simple player
                this.player = this.physics.add.sprite(width / 2 + 100, height / 2, 'player');
                this.player.setCollideWorldBounds(true);
                
                // Simple ball
                this.ball = this.physics.add.sprite(width / 2, height / 2, 'ball');
                this.ball.setCollideWorldBounds(true);
                this.ball.setBounce(0.7);
                
                // Simple HUD
                this.add.rectangle(width / 2, 40, width, 60, 0x2C3E50, 0.8);
                this.add.text(width / 2, 40, 'MINIMAL GAME WORKING!', {
                    fontSize: '20px',
                    fontFamily: 'Arial',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                // Controls
                this.cursors = this.input.keyboard.createCursorKeys();
                this.wasd = this.input.keyboard.addKeys('W,S,A,D');
                this.spaceKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.SPACE);
                this.escKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.ESC);
                
                // Instructions
                this.add.text(width / 2, height - 30, 'WASD: Move | SPACE: Kick Ball | ESC: Menu', {
                    fontSize: '14px',
                    fontFamily: 'Arial',
                    color: '#ffffff',
                    alpha: 0.8
                }).setOrigin(0.5);
                
                log('MinimalGameScene: Game created successfully', 'success');
            }
            
            update() {
                // Player movement
                if (this.wasd.W.isDown) this.player.setVelocityY(-200);
                else if (this.wasd.S.isDown) this.player.setVelocityY(200);
                else this.player.setVelocityY(0);
                
                if (this.wasd.A.isDown) this.player.setVelocityX(-200);
                else if (this.wasd.D.isDown) this.player.setVelocityX(200);
                else this.player.setVelocityX(0);
                
                // Ball kick
                if (Phaser.Input.Keyboard.JustDown(this.spaceKey)) {
                    const angle = Phaser.Math.Angle.Between(this.player.x, this.player.y, this.ball.x, this.ball.y);
                    this.ball.setVelocity(Math.cos(angle) * 300, Math.sin(angle) * 300);
                }
                
                // Back to menu
                if (Phaser.Input.Keyboard.JustDown(this.escKey)) {
                    this.scene.start('MinimalMenuScene');
                }
            }
        }
        
        // Game configuration
        const config = {
            type: Phaser.AUTO,
            width: 1024,
            height: 640,
            canvas: document.getElementById('game-canvas'),
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 },
                    debug: false
                }
            },
            scene: [MinimalPreloadScene, MinimalMenuScene, MinimalGameScene],
            backgroundColor: 0x1e3c72,
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };
        
        // Initialize minimal game
        try {
            log('Initializing minimal Phaser game...', 'info');
            const game = new Phaser.Game(config);
            log('Minimal game initialized successfully!', 'success');
        } catch (error) {
            log('Error initializing minimal game: ' + error.message, 'error');
            console.error('Full error:', error);
        }
    </script>
</body>
</html>
