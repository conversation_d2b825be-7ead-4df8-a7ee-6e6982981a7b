<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Simple - Water Polo Clash</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #2c3e50;
            color: white;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        .loaded {
            background: #27ae60;
        }
        .missing {
            background: #e74c3c;
        }
        .info {
            background: #3498db;
        }
    </style>
</head>
<body>
    <h1>Water Polo Clash - Simple Debug</h1>
    <div id="status">
        <div class="info">Starting debug...</div>
    </div>

    <script>
        const statusDiv = document.getElementById('status');
        
        function addStatus(message, type = 'info') {
            console.log(message);
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            statusDiv.appendChild(div);
        }

        // Capture errors
        window.addEventListener('error', (event) => {
            addStatus(`ERROR: ${event.message} at ${event.filename}:${event.lineno}`, 'missing');
        });

        addStatus('Loading Phaser...', 'info');
    </script>

    <!-- Load Phaser first -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <script>
        if (typeof Phaser !== 'undefined') {
            addStatus('✓ Phaser loaded successfully', 'loaded');
        } else {
            addStatus('✗ Phaser failed to load', 'missing');
        }
        addStatus('Loading game scripts...', 'info');
    </script>
    
    <!-- Load game scripts in exact same order as index.html -->
    <script src="js/config.js"></script>
    <script src="js/scenes/PreloadScene.js"></script>
    <script src="js/scenes/MainMenuScene.js"></script>
    <script src="js/scenes/GameScene.js"></script>
    <script src="js/entities/Player.js"></script>
    <script src="js/entities/Ball.js"></script>
    <script src="js/systems/InputManager.js"></script>
    <script src="js/systems/GameRules.js"></script>
    <script src="js/systems/AIManager.js"></script>
    <script src="js/systems/PowerUpManager.js"></script>
    <script src="js/ui/HUD.js"></script>

    <script>
        // Check all classes after loading
        const requiredClasses = [
            'GameConfig', 'PreloadScene', 'MainMenuScene', 'GameScene',
            'Player', 'Ball', 'InputManager', 'GameRules', 'AIManager', 'PowerUpManager', 'HUD'
        ];

        addStatus('Checking loaded classes...', 'info');

        let loadedCount = 0;
        let missingCount = 0;

        requiredClasses.forEach(className => {
            if (typeof window[className] !== 'undefined') {
                addStatus(`✓ ${className} loaded`, 'loaded');
                loadedCount++;
            } else {
                addStatus(`✗ ${className} missing`, 'missing');
                missingCount++;
            }
        });

        addStatus(`Summary: ${loadedCount} loaded, ${missingCount} missing`, loadedCount === requiredClasses.length ? 'loaded' : 'missing');

        if (loadedCount === requiredClasses.length) {
            addStatus('All classes loaded successfully! Testing game creation...', 'loaded');
            
            // Test creating a minimal Phaser game
            try {
                const testConfig = {
                    type: Phaser.AUTO,
                    width: 800,
                    height: 600,
                    scene: [PreloadScene],
                    physics: {
                        default: 'arcade',
                        arcade: {
                            gravity: { y: 0 },
                            debug: false
                        }
                    },
                    backgroundColor: GameConfig.POOL.WATER_COLOR
                };
                
                addStatus('Creating test Phaser game...', 'info');
                const testGame = new Phaser.Game(testConfig);
                addStatus('✓ Test game created successfully!', 'loaded');
                
                // Clean up after 3 seconds
                setTimeout(() => {
                    testGame.destroy(true);
                    addStatus('Test game cleaned up', 'info');
                }, 3000);
                
            } catch (error) {
                addStatus(`✗ Error creating test game: ${error.message}`, 'missing');
            }
        } else {
            addStatus('Cannot proceed - missing required classes', 'missing');
        }
    </script>
</body>
</html>
