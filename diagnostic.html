<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Polo Clash - Diagnostic</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #1e3c72;
            color: white;
            font-family: Arial, sans-serif;
        }
        #diagnostic-output {
            background: rgba(0,0,0,0.3);
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .error { color: #ff6b6b; }
        .success { color: #51cf66; }
        .info { color: #74c0fc; }
        .warning { color: #ffd43b; }
        #game-container {
            border: 3px solid #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
            background: #000;
            display: inline-block;
        }
        button {
            background: #3498DB;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin: 5px;
            cursor: pointer;
        }
        button:hover { background: #2980B9; }
    </style>
</head>
<body>
    <h1>🔧 Water Polo Clash - Comprehensive Diagnostic</h1>
    
    <div>
        <button onclick="runFullDiagnostic()">Run Full Diagnostic</button>
        <button onclick="testGameLoad()">Test Game Load</button>
        <button onclick="clearOutput()">Clear Output</button>
    </div>
    
    <div id="diagnostic-output">
        <div class="info">Diagnostic ready. Click "Run Full Diagnostic" to start.</div>
    </div>
    
    <div id="game-container">
        <div style="color: white; padding: 20px; text-align: center;">
            Game will load here after diagnostic
        </div>
        <canvas id="game-canvas" style="display: none;"></canvas>
    </div>

    <script>
        const output = document.getElementById('diagnostic-output');
        
        function log(message, type = 'info') {
            console.log(message);
            const div = document.createElement('div');
            div.className = type;
            div.textContent = new Date().toLocaleTimeString() + ': ' + message;
            output.appendChild(div);
            output.scrollTop = output.scrollHeight;
        }
        
        function clearOutput() {
            output.innerHTML = '<div class="info">Output cleared.</div>';
        }
        
        async function runFullDiagnostic() {
            clearOutput();
            log('🔍 Starting comprehensive diagnostic...', 'info');
            
            // Test 1: Basic environment
            log('📋 Testing basic environment...', 'info');
            testBasicEnvironment();
            
            // Test 2: File availability
            log('📁 Testing file availability...', 'info');
            await testFileAvailability();
            
            // Test 3: Load Phaser
            log('🎮 Testing Phaser loading...', 'info');
            await loadPhaser();
            
            // Test 4: Load game files
            log('📜 Loading game files...', 'info');
            await loadGameFiles();
            
            // Test 5: Test class definitions
            log('🏗️ Testing class definitions...', 'info');
            testClassDefinitions();
            
            log('✅ Diagnostic complete!', 'success');
        }
        
        function testBasicEnvironment() {
            // Canvas test
            const canvas = document.getElementById('game-canvas');
            if (canvas) {
                log('✅ Canvas element found', 'success');
            } else {
                log('❌ Canvas element not found', 'error');
            }
            
            // WebGL test
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (gl) {
                log('✅ WebGL supported', 'success');
            } else {
                log('⚠️ WebGL not supported, will use Canvas', 'warning');
            }
            
            // Touch support
            if ('ontouchstart' in window) {
                log('✅ Touch events supported', 'success');
            } else {
                log('ℹ️ Desktop mode (no touch)', 'info');
            }
        }
        
        async function testFileAvailability() {
            const files = [
                'js/config.js',
                'js/main.js',
                'js/scenes/PreloadScene.js',
                'js/scenes/MainMenuScene.js',
                'js/scenes/GameScene.js',
                'js/entities/Player.js',
                'js/entities/Ball.js',
                'js/systems/InputManager.js',
                'js/systems/GameRules.js',
                'js/systems/AIManager.js',
                'js/systems/PowerUpManager.js',
                'js/ui/HUD.js'
            ];
            
            for (const file of files) {
                try {
                    const response = await fetch(file);
                    if (response.ok) {
                        log('✅ ' + file, 'success');
                    } else {
                        log('❌ ' + file + ' (HTTP ' + response.status + ')', 'error');
                    }
                } catch (error) {
                    log('❌ ' + file + ' (Network error)', 'error');
                }
            }
        }
        
        async function loadPhaser() {
            return new Promise((resolve) => {
                if (typeof Phaser !== 'undefined') {
                    log('✅ Phaser already loaded (v' + Phaser.VERSION + ')', 'success');
                    resolve();
                    return;
                }
                
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js';
                script.onload = () => {
                    log('✅ Phaser loaded successfully (v' + Phaser.VERSION + ')', 'success');
                    resolve();
                };
                script.onerror = () => {
                    log('❌ Failed to load Phaser', 'error');
                    resolve();
                };
                document.head.appendChild(script);
            });
        }
        
        async function loadGameFiles() {
            const files = [
                'js/config.js',
                'js/scenes/PreloadScene.js',
                'js/scenes/MainMenuScene.js',
                'js/scenes/GameScene.js',
                'js/entities/Player.js',
                'js/entities/Ball.js',
                'js/systems/InputManager.js',
                'js/systems/GameRules.js',
                'js/systems/AIManager.js',
                'js/systems/PowerUpManager.js',
                'js/ui/HUD.js',
                'js/main.js'
            ];
            
            for (const file of files) {
                try {
                    await loadScript(file);
                    log('✅ Loaded ' + file, 'success');
                } catch (error) {
                    log('❌ Failed to load ' + file + ': ' + error.message, 'error');
                }
            }
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = resolve;
                script.onerror = () => reject(new Error('Script load failed'));
                document.head.appendChild(script);
            });
        }
        
        function testClassDefinitions() {
            const classes = [
                'GameConfig', 'GameUtils', 'GameStates', 'Teams', 'PlayerPositions',
                'PreloadScene', 'MainMenuScene', 'GameScene',
                'Player', 'Ball',
                'InputManager', 'GameRules', 'AIManager', 'PowerUpManager',
                'HUD', 'WaterPoloGame'
            ];
            
            let allDefined = true;
            classes.forEach(className => {
                if (typeof window[className] !== 'undefined') {
                    log('✅ ' + className + ' defined', 'success');
                } else {
                    log('❌ ' + className + ' NOT defined', 'error');
                    allDefined = false;
                }
            });
            
            if (allDefined) {
                log('🎉 All classes defined successfully!', 'success');
            } else {
                log('⚠️ Some classes missing - game may not work', 'warning');
            }
        }
        
        async function testGameLoad() {
            log('🎮 Testing game initialization...', 'info');
            
            if (typeof WaterPoloGame === 'undefined') {
                log('❌ WaterPoloGame class not available', 'error');
                return;
            }
            
            try {
                const canvas = document.getElementById('game-canvas');
                canvas.style.display = 'block';
                
                const game = new WaterPoloGame();
                log('✅ WaterPoloGame instance created', 'success');
                
                setTimeout(() => {
                    try {
                        game.init();
                        log('✅ Game initialization started', 'success');
                    } catch (error) {
                        log('❌ Game initialization failed: ' + error.message, 'error');
                    }
                }, 100);
                
            } catch (error) {
                log('❌ Failed to create game: ' + error.message, 'error');
            }
        }
    </script>
</body>
</html>
