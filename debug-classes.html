<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Classes - Water Polo Clash</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #2c3e50;
            color: white;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .loaded {
            background: #27ae60;
        }
        .missing {
            background: #e74c3c;
        }
        .error {
            background: #f39c12;
        }
    </style>
</head>
<body>
    <h1>Water Polo Clash - Class Loading Debug</h1>
    <div id="status"></div>

    <!-- Load Phaser first -->
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    
    <!-- Load game scripts in order -->
    <script src="js/config.js"></script>
    <script src="js/scenes/PreloadScene.js"></script>
    <script src="js/scenes/MainMenuScene.js"></script>
    <script src="js/scenes/GameScene.js"></script>
    <script src="js/entities/Player.js"></script>
    <script src="js/entities/Ball.js"></script>
    <script src="js/systems/InputManager.js"></script>
    <script src="js/systems/GameRules.js"></script>
    <script src="js/systems/AIManager.js"></script>
    <script src="js/systems/PowerUpManager.js"></script>
    <script src="js/ui/HUD.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const statusDiv = document.getElementById('status');
            
            // Check if Phaser is loaded
            if (typeof Phaser !== 'undefined') {
                statusDiv.innerHTML += '<div class="status loaded">✓ Phaser library loaded successfully</div>';
            } else {
                statusDiv.innerHTML += '<div class="status missing">✗ Phaser library not loaded</div>';
            }

            // List of required classes
            const requiredClasses = [
                'GameConfig', 'PreloadScene', 'MainMenuScene', 'GameScene',
                'Player', 'Ball', 'InputManager', 'GameRules', 'AIManager', 'PowerUpManager', 'HUD'
            ];

            let loadedClasses = [];
            let missingClasses = [];

            requiredClasses.forEach(className => {
                try {
                    if (typeof window[className] !== 'undefined') {
                        loadedClasses.push(className);
                        statusDiv.innerHTML += `<div class="status loaded">✓ ${className} loaded</div>`;
                    } else {
                        missingClasses.push(className);
                        statusDiv.innerHTML += `<div class="status missing">✗ ${className} missing</div>`;
                    }
                } catch (error) {
                    missingClasses.push(className);
                    statusDiv.innerHTML += `<div class="status error">⚠ ${className} error: ${error.message}</div>`;
                }
            });

            // Summary
            statusDiv.innerHTML += '<h2>Summary</h2>';
            statusDiv.innerHTML += `<div class="status loaded">Loaded: ${loadedClasses.length} classes</div>`;
            statusDiv.innerHTML += `<div class="status missing">Missing: ${missingClasses.length} classes</div>`;

            if (missingClasses.length > 0) {
                statusDiv.innerHTML += '<h3>Missing Classes:</h3>';
                missingClasses.forEach(className => {
                    statusDiv.innerHTML += `<div class="status missing">${className}</div>`;
                });
            }

            // Check for JavaScript errors
            window.addEventListener('error', (event) => {
                statusDiv.innerHTML += `<div class="status error">JavaScript Error: ${event.message} at ${event.filename}:${event.lineno}</div>`;
            });
        });
    </script>
</body>
</html>
